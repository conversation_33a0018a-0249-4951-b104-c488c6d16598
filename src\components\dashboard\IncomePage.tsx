import React, { useState } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { RotateCcw, Save, ArrowRight } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

const IncomePage: React.FC = () => {
  // State for full surrender section
  const [fullSurrender, setFullSurrender] = useState({
    incomeByFullSurrender: '',
    modifyFullSurrenderEnabled: false,
    selectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    ageRange: {
      start: 40,
      end: 100
    },
    policyYearRange: {
      start: 1,
      end: 100
    },
    calendarYearRange: {
      start: 2024,
      end: 2100
    },
    isEditing: false,
    tableData: [] as TableRowData[]
  });

  // State for withdrawal section
  const [withdrawalModel, setWithdrawalModel] = useState({
    fixedAmount: '',
    percentOfCash: '',
    flatAnnualAmount: '',
    modifyFlatAnnualEnabled: false,
    flatAnnualSelectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    flatAnnualAgeRange: {
      start: 40,
      end: 100
    },
    flatAnnualPolicyYearRange: {
      start: 1,
      end: 100
    },
    flatAnnualCalendarYearRange: {
      start: 2024,
      end: 2100
    },
    flatAnnualIsEditing: false,
    flatAnnualTableData: [] as TableRowData[],
    increasingStartAmount: '',
    increasingPercentage: '',
    increasingEndAge: 65,
  });

  // State for loan section
  const [loanModel, setLoanModel] = useState({
    fixedAmount: '',
    percentOfCash: '',
    annualAmount: '',
    premiumFinancing: false,
    modifyAnnualAmountEnabled: false,
    annualAmountSelectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    annualAmountAgeRange: {
      start: 40,
      end: 100
    },
    annualAmountPolicyYearRange: {
      start: 1,
      end: 100
    },
    annualAmountCalendarYearRange: {
      start: 2024,
      end: 2100
    },
    annualAmountIsEditing: false,
    annualAmountTableData: [] as TableRowData[]
  });

  type TableRowData = {
    age: number;
    policyYear: string;
    calendarYear: number;
    amount: number;
  };

  const { selectedCustomerData, selectedPolicyData, setActiveTab } = useDashboard();

  // Calculate current age from DOB
  const calculateCurrentAge = (): number => {
    if (!selectedCustomerData?.details?.DOB) return 40; // Default age

    const dob = selectedCustomerData.details.DOB;
    let birthDate: Date;

    if (dob.includes('.')) {
      const dobParts = dob.split('.');
      if (dobParts.length !== 3) return 40;
      const [day, month, year] = dobParts.map(Number);
      birthDate = new Date(year, month - 1, day);
    } else if (dob.includes('/')) {
      const dobParts = dob.split('/');
      if (dobParts.length !== 3) return 40;
      const [first, second, year] = dobParts.map(Number);
      birthDate = new Date(year, first - 1, second);
    } else if (dob.includes('-')) {
      birthDate = new Date(dob);
    } else {
      return 40;
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return Math.max(0, age);
  };

  // Calculate current policy year from issue date
  const calculateCurrentPolicyYear = (): number => {
    const issueDate = (selectedPolicyData as any)?.issueDate || (selectedPolicyData as any)?.policyStartDate;
    if (issueDate) {
      const issue = new Date(issueDate);
      const today = new Date();
      const yearsDiff = today.getFullYear() - issue.getFullYear();
      const monthsDiff = today.getMonth() - issue.getMonth();
      const daysDiff = today.getDate() - issue.getDate();

      let totalMonths = yearsDiff * 12 + monthsDiff;
      if (daysDiff >= 0) {
        totalMonths += 1;
      }

      const policyYear = Math.max(1, Math.ceil(totalMonths / 12));
      return policyYear;
    }
    return 1;
  };

  // Get current calendar year
  const getCurrentYear = (): number => {
    return new Date().getFullYear();
  };

  // Initialize ranges with actual values
  React.useEffect(() => {
    const currentAge = calculateCurrentAge();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const currentYear = getCurrentYear();

    setFullSurrender(prev => ({
      ...prev,
      ageRange: {
        start: currentAge,
        end: 100
      },
      policyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      calendarYearRange: {
        start: currentYear,
        end: 2100
      }
    }));

    setWithdrawalModel(prev => ({
      ...prev,
      flatAnnualAgeRange: {
        start: currentAge,
        end: 100
      },
      flatAnnualPolicyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      flatAnnualCalendarYearRange: {
        start: currentYear,
        end: 2100
      },
      increasingEndAge: currentAge + 20
    }));

    setLoanModel(prev => ({
      ...prev,
      annualAmountAgeRange: {
        start: currentAge,
        end: 100
      },
      annualAmountPolicyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      annualAmountCalendarYearRange: {
        start: currentYear,
        end: 2100
      }
    }));
  }, [selectedCustomerData, selectedPolicyData]);

  // Save all scenarios (placeholder)
  const handleSaveAll = () => {
    alert('All selected income scenarios saved!');
  };

  // Reset all scenario state
  const handleResetScenarios = () => {
    const currentAge = calculateCurrentAge();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const currentYear = getCurrentYear();

    setFullSurrender({
      incomeByFullSurrender: '',
      modifyFullSurrenderEnabled: false,
      selectedTypes: {
        age: false,
        policyYear: false,
        calendarYear: false
      },
      ageRange: {
        start: currentAge,
        end: 100
      },
      policyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      calendarYearRange: {
        start: currentYear,
        end: 2100
      },
      isEditing: false,
      tableData: []
    });

    setWithdrawalModel({
      fixedAmount: '',
      percentOfCash: '',
      flatAnnualAmount: '',
      modifyFlatAnnualEnabled: false,
      flatAnnualSelectedTypes: {
        age: false,
        policyYear: false,
        calendarYear: false
      },
      flatAnnualAgeRange: {
        start: currentAge,
        end: 100
      },
      flatAnnualPolicyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      flatAnnualCalendarYearRange: {
        start: currentYear,
        end: 2100
      },
      flatAnnualIsEditing: false,
      flatAnnualTableData: [],
      increasingStartAmount: '',
      increasingPercentage: '',
      increasingEndAge: currentAge + 20,
    });

    setLoanModel({
      fixedAmount: '',
      percentOfCash: '',
      annualAmount: '',
      premiumFinancing: false,
      modifyAnnualAmountEnabled: false,
      annualAmountSelectedTypes: {
        age: false,
        policyYear: false,
        calendarYear: false
      },
      annualAmountAgeRange: {
        start: currentAge,
        end: 100
      },
      annualAmountPolicyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      annualAmountCalendarYearRange: {
        start: currentYear,
        end: 2100
      },
      annualAmountIsEditing: false,
      annualAmountTableData: []
    });

    alert('All income scenarios have been reset!');
  };



  return (
    <div className="space-y-6">
      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Income illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <div className="min-h-screen bg-gray-50 p-4 space-y-8">
          {/* Top Description */}
          <Card className="mb-8">
            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
              <p className="text-lg text-gray-800 leading-relaxed">
                The income from the policy can be through either a partial withdrawal of cash value or through a new or top-up loan. You can model also a full surrender now or a later date.
              </p>
            </div>
          </Card>

          {/* 1. Full Surrender/Income Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">1. Full surrender/income</h2>
            <div className="space-y-4">
              {/* a. Income by full surrender */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-black">a. Income by full surrender</h3>
                <Select
                  value={fullSurrender.incomeByFullSurrender}
                  onChange={(e) => setFullSurrender(prev => ({ ...prev, incomeByFullSurrender: e.target.value }))}
                  options={[
                    { value: '', label: 'Select option' },
                    { value: 'manual', label: 'Manual entry' }
                  ]}
                  className="text-black"
                />
                {fullSurrender.incomeByFullSurrender === 'manual' && (
                  <div className="mt-2 ml-6">
                    <Input
                      label="Amount: $"
                      value=""
                      onChange={() => {}}
                      className="text-black placeholder-black"
                      placeholder="Enter amount"
                    />
                  </div>
                )}
              </div>

              {/* b. Modify full surrender/income */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black">
                  <input
                    type="checkbox"
                    checked={fullSurrender.modifyFullSurrenderEnabled}
                    onChange={(e) => setFullSurrender(prev => ({ ...prev, modifyFullSurrenderEnabled: e.target.checked }))}
                    className="mr-2"
                  />
                  b. Modify full surrender/income
                </label>
                {fullSurrender.modifyFullSurrenderEnabled && (
                  <div className="mt-4 space-y-6">
                    {/* Single Container for Type Selection and Range Controls */}
                    <div className="bg-white p-6 rounded-lg border border-gray-300">
                      {/* Type Selection Checkboxes - allow multiple selections */}
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={fullSurrender.selectedTypes.age}
                            onChange={() => setFullSurrender(prev => ({
                              ...prev,
                              selectedTypes: {
                                ...prev.selectedTypes,
                                age: !prev.selectedTypes.age
                              }
                            }))}
                            className="mr-2"
                          />
                          Age
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={fullSurrender.selectedTypes.policyYear}
                            onChange={() => setFullSurrender(prev => ({
                              ...prev,
                              selectedTypes: {
                                ...prev.selectedTypes,
                                policyYear: !prev.selectedTypes.policyYear
                              }
                            }))}
                            className="mr-2"
                          />
                          Policy Year
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={fullSurrender.selectedTypes.calendarYear}
                            onChange={() => setFullSurrender(prev => ({
                              ...prev,
                              selectedTypes: {
                                ...prev.selectedTypes,
                                calendarYear: !prev.selectedTypes.calendarYear
                              }
                            }))}
                            className="mr-2"
                          />
                          Calendar Year
                        </label>
                      </div>

                      {/* Age Range Toggle Bars */}
                      {fullSurrender.selectedTypes.age && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, start: Math.max(calculateCurrentAge(), prev.ageRange.start - 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {fullSurrender.ageRange.start}
                                </div>
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, start: Math.min(100, prev.ageRange.start + 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Age</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, end: Math.max(prev.ageRange.start, prev.ageRange.end - 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {fullSurrender.ageRange.end}
                                </div>
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, end: Math.min(100, prev.ageRange.end + 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Policy Year Range Toggle Bars */}
                      {fullSurrender.selectedTypes.policyYear && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, start: Math.max(calculateCurrentPolicyYear(), prev.policyYearRange.start - 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {fullSurrender.policyYearRange.start}
                                </div>
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, start: Math.min(100, prev.policyYearRange.start + 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, end: Math.max(prev.policyYearRange.start, prev.policyYearRange.end - 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {fullSurrender.policyYearRange.end}
                                </div>
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, end: Math.min(100, prev.policyYearRange.end + 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Calendar Year Range Toggle Bars */}
                      {fullSurrender.selectedTypes.calendarYear && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {fullSurrender.calendarYearRange.start}
                                </div>
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, start: Math.min(2100, prev.calendarYearRange.start + 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, end: Math.max(prev.calendarYearRange.start, prev.calendarYearRange.end - 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {fullSurrender.calendarYearRange.end}
                                </div>
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, end: Math.min(2100, prev.calendarYearRange.end + 1) }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* 2. Withdrawal Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">2. Do you want to model policy withdrawals/Income (either one-time or recurring)?</h2>
            <div className="space-y-4">
              {/* a. Fixed amount or percentage */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-black">a. Fixed amount or % of available cash value</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Fixed amount: $"
                    value={withdrawalModel.fixedAmount}
                    onChange={e => {
                      if (e.target.value) {
                        setWithdrawalModel(prev => ({ ...prev, fixedAmount: e.target.value, percentOfCash: '' }));
                      } else {
                        setWithdrawalModel(prev => ({ ...prev, fixedAmount: e.target.value }));
                      }
                    }}
                    className="text-black placeholder-black"
                    placeholder="Amount"
                    disabled={!!withdrawalModel.percentOfCash}
                  />
                  <div className="flex items-center space-x-2">
                    <span className="text-black">or % of available cash value:</span>
                    <Input
                      value={withdrawalModel.percentOfCash}
                      onChange={e => {
                        if (e.target.value) {
                          setWithdrawalModel(prev => ({ ...prev, percentOfCash: e.target.value, fixedAmount: '' }));
                        } else {
                          setWithdrawalModel(prev => ({ ...prev, percentOfCash: e.target.value }));
                        }
                      }}
                      className="text-black placeholder-black w-20"
                      placeholder="%"
                      disabled={!!withdrawalModel.fixedAmount}
                    />
                    <span className="text-black">% now</span>
                  </div>
                </div>
              </div>

              {/* b. Flat Annual Amount */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-black">b. Flat Annual Amount</h3>
                <Select
                  value={withdrawalModel.flatAnnualAmount}
                  onChange={(e) => setWithdrawalModel(prev => ({ ...prev, flatAnnualAmount: e.target.value }))}
                  options={[
                    { value: '', label: 'Select option' },
                    { value: 'manual', label: 'Manual entry' }
                  ]}
                  className="text-black"
                />
                {withdrawalModel.flatAnnualAmount === 'manual' && (
                  <div className="mt-2 space-y-4">
                    <Input
                      label="Amount: $"
                      value=""
                      onChange={() => {}}
                      className="text-black placeholder-black"
                      placeholder="Enter amount"
                    />
                    <label className="flex items-center text-lg font-semibold text-black">
                      <input
                        type="checkbox"
                        checked={withdrawalModel.modifyFlatAnnualEnabled}
                        onChange={(e) => setWithdrawalModel(prev => ({ ...prev, modifyFlatAnnualEnabled: e.target.checked }))}
                        className="mr-2"
                      />
                      Modify the flat annual amount by year
                    </label>
                    {/* Add the same toggle bar structure as in full surrender section */}
                  </div>
                )}
              </div>

              {/* c. Increasing Income stream */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-black">c. Increasing Income stream</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-black">Starting amount</span>
                    <Input
                      value={withdrawalModel.increasingStartAmount}
                      onChange={e => setWithdrawalModel(prev => ({ ...prev, increasingStartAmount: e.target.value }))}
                      className="text-black placeholder-black"
                      placeholder="Amount"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-black">by</span>
                    <Input
                      value={withdrawalModel.increasingPercentage}
                      onChange={e => setWithdrawalModel(prev => ({ ...prev, increasingPercentage: e.target.value }))}
                      className="text-black placeholder-black w-20"
                      placeholder="%"
                    />
                    <span className="text-black">% every year from now up to age</span>
                  </div>
                  <div>
                    <label className="block text-sm font-bold text-black mb-2">End Age</label>
                    <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                      <button
                        onClick={() => setWithdrawalModel(prev => ({
                          ...prev,
                          increasingEndAge: Math.max(calculateCurrentAge(), prev.increasingEndAge - 1)
                        }))}
                        className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                      >
                        ◀
                      </button>
                      <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                        {withdrawalModel.increasingEndAge}
                      </div>
                      <button
                        onClick={() => setWithdrawalModel(prev => ({
                          ...prev,
                          increasingEndAge: Math.min(100, prev.increasingEndAge + 1)
                        }))}
                        className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                      >
                        ▶
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* 3. Policy Loan Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">3. Your Policy is eligible for a loan. Or Do you want to model an income stream using policy loans (e.g., tax-free retirement income strategy)? Do you want us to illustrate?</h2>
            <div className="space-y-4">
              {/* a. Fixed amount or percentage */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-black">a. Fixed amount or % of available cash value</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Fixed amount: $"
                    value={loanModel.fixedAmount}
                    onChange={e => {
                      if (e.target.value) {
                        setLoanModel(prev => ({ ...prev, fixedAmount: e.target.value, percentOfCash: '' }));
                      } else {
                        setLoanModel(prev => ({ ...prev, fixedAmount: e.target.value }));
                      }
                    }}
                    className="text-black placeholder-black"
                    placeholder="Amount"
                    disabled={!!loanModel.percentOfCash}
                  />
                  <div className="flex items-center space-x-2">
                    <span className="text-black">or % of available cash value:</span>
                    <Input
                      value={loanModel.percentOfCash}
                      onChange={e => {
                        if (e.target.value) {
                          setLoanModel(prev => ({ ...prev, percentOfCash: e.target.value, fixedAmount: '' }));
                        } else {
                          setLoanModel(prev => ({ ...prev, percentOfCash: e.target.value }));
                        }
                      }}
                      className="text-black placeholder-black w-20"
                      placeholder="%"
                      disabled={!!loanModel.fixedAmount}
                    />
                    <span className="text-black">% now</span>
                  </div>
                </div>
              </div>

              {/* b. Annual amount */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-black">b. Annual amount is an</h3>
                <Input
                  value={loanModel.annualAmount}
                  onChange={e => setLoanModel(prev => ({ ...prev, annualAmount: e.target.value }))}
                  className="text-black placeholder-black"
                  placeholder="Enter amount"
                />
              </div>

              {/* c. Loan for paying the premium */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black">
                  <input
                    type="checkbox"
                    checked={loanModel.premiumFinancing}
                    onChange={e => setLoanModel(prev => ({ ...prev, premiumFinancing: e.target.checked }))}
                    className="mr-2"
                  />
                  c. Loan for paying the premium (Premium financing)
                </label>
              </div>

              {/* d. Modify the annual amount by year */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black">
                  <input
                    type="checkbox"
                    checked={loanModel.modifyAnnualAmountEnabled}
                    onChange={(e) => setLoanModel(prev => ({ ...prev, modifyAnnualAmountEnabled: e.target.checked }))}
                    className="mr-2"
                  />
                  d. Modify the annual amount by year
                </label>
                {/* Add the same toggle bar structure as in full surrender section */}
              </div>
            </div>
          </Card>

          {/* Save and Reset Buttons (refactored to match AsIsPage) */}
          <div className="flex flex-wrap gap-4 justify-center mt-8">
            <Button onClick={handleSaveAll}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold">
              <Save className="w-4 h-4" />
              <span>Save All Selected Scenarios</span>
            </Button>
            <Button onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold">
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
            <Button
              onClick={() => alert('Proceed to next step (e.g., Loan Repayment Illustration)')}
              className="flex items-center space-x-2">
              <span>Proceed to Loan Repayment Illustration</span>
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default IncomePage;